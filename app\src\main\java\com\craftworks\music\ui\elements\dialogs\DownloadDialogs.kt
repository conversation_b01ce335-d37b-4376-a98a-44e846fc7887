package com.craftworks.music.ui.elements.dialogs

import android.content.Context
import android.os.Environment
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Switch
import androidx.compose.runtime.collectAsState
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.KeyboardArrowUp
import androidx.compose.material.icons.rounded.KeyboardArrowRight
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.craftworks.music.R
import com.craftworks.music.managers.SettingsManager
import com.craftworks.music.providers.local.LocalProvider
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun DirectoryPickerDialog(
    setShowDialog: (Boolean) -> Unit,
    onDirectorySelected: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()

    var errorMessage by remember { mutableStateOf<String?>(null) }
    
    var currentPath by remember {
        mutableStateOf(
            context.getExternalFilesDir(Environment.DIRECTORY_MUSIC)?.absolutePath
                ?: Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC).absolutePath
        )
    }
    var directories by remember { mutableStateOf(listOf<File>()) }
    
    // Load directories for current path
    LaunchedEffect(currentPath) {
        try {
            val currentDir = File(currentPath)
            if (currentDir.exists() && currentDir.isDirectory && currentDir.canRead()) {
                directories = currentDir.listFiles()?.filter {
                    it.isDirectory && it.canRead() && !it.name.startsWith(".")
                }?.sortedBy { it.name } ?: emptyList()
                Log.d("DIRECTORY_PICKER", "Loaded ${directories.size} directories from $currentPath")
            } else {
                Log.w("DIRECTORY_PICKER", "Cannot access directory: $currentPath")
                // Fallback to accessible directories
                val fallbackDirs = listOf(
                    context.getExternalFilesDir(Environment.DIRECTORY_MUSIC),
                    context.getExternalFilesDir(Environment.DIRECTORY_DOWNLOADS),
                    context.filesDir,
                    File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_MUSIC).absolutePath),
                    File(Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS).absolutePath)
                ).filterNotNull().filter { it.exists() && it.canRead() }

                if (fallbackDirs.isNotEmpty()) {
                    currentPath = fallbackDirs.first().absolutePath
                    directories = fallbackDirs.first().listFiles()?.filter {
                        it.isDirectory && it.canRead() && !it.name.startsWith(".")
                    }?.sortedBy { it.name } ?: emptyList()
                    Log.d("DIRECTORY_PICKER", "Fallback to ${fallbackDirs.first().absolutePath}")
                } else {
                    directories = emptyList()
                    Log.e("DIRECTORY_PICKER", "No accessible directories found")
                }
            }
        } catch (e: Exception) {
            Log.e("DIRECTORY_PICKER", "Error loading directories: ${e.message}")
            directories = emptyList()
        }
    }

    Dialog(
        onDismissRequest = { setShowDialog(false) },
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = Modifier
                .widthIn(min = 280.dp, max = 400.dp)
                .shadow(8.dp, RoundedCornerShape(12.dp))
                .clip(RoundedCornerShape(12.dp))
                .background(MaterialTheme.colorScheme.surface),
            color = Color.Transparent
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(R.string.Settings_DownloadDirectory),
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    
                    IconButton(onClick = { setShowDialog(false) }) {
                        Icon(
                            imageVector = Icons.Rounded.Close,
                            contentDescription = "Close",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Current path
                Text(
                    text = currentPath,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.7f),
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp)
                        .background(
                            MaterialTheme.colorScheme.surfaceVariant,
                            RoundedCornerShape(8.dp)
                        )
                        .padding(12.dp)
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                // Directory list
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(300.dp)
                ) {
                    // Parent directory option
                    if (currentPath != "/") {
                        item {
                            DirectoryItem(
                                name = "..",
                                isParent = true,
                                onClick = {
                                    val parentFile = File(currentPath).parentFile
                                    if (parentFile != null && parentFile.canRead()) {
                                        currentPath = parentFile.absolutePath
                                    }
                                }
                            )
                        }
                    }
                    
                    // Directory items
                    items(directories) { directory ->
                        DirectoryItem(
                            name = directory.name,
                            onClick = {
                                currentPath = directory.absolutePath
                            }
                        )
                    }
                }
                
                Spacer(modifier = Modifier.height(16.dp))

                // Error message
                errorMessage?.let { message ->
                    Card(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(bottom = 8.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = MaterialTheme.colorScheme.errorContainer
                        )
                    ) {
                        Text(
                            text = message,
                            modifier = Modifier.padding(12.dp),
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            style = MaterialTheme.typography.bodySmall
                        )
                    }
                }

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.End)
                ) {
                    Button(
                        onClick = { setShowDialog(false) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.surfaceVariant,
                            contentColor = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    ) {
                        Text("Cancel")
                    }
                    
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                try {
                                    errorMessage = null
                                    // Verify the directory is writable
                                    val selectedDir = File(currentPath)
                                    when {
                                        !selectedDir.exists() -> {
                                            errorMessage = "Directory does not exist"
                                        }
                                        !selectedDir.canWrite() -> {
                                            errorMessage = "Directory is not writable. Please choose a different location."
                                        }
                                        else -> {
                                            SettingsManager(context).setDownloadDirectory(currentPath)
                                            onDirectorySelected(currentPath)
                                            setShowDialog(false)
                                            Log.d("DIRECTORY_PICKER", "Selected directory: $currentPath")
                                        }
                                    }
                                } catch (e: Exception) {
                                    errorMessage = "Error selecting directory: ${e.message}"
                                    Log.e("DIRECTORY_PICKER", "Error selecting directory: ${e.message}")
                                }
                            }
                        }
                    ) {
                        Text("Select")
                    }
                }
            }
        }
    }
}

@Composable
private fun DirectoryItem(
    name: String,
    isParent: Boolean = false,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 8.dp, horizontal = 4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = if (isParent) Icons.Rounded.KeyboardArrowUp else Icons.Rounded.KeyboardArrowRight,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.primary,
            modifier = Modifier.size(24.dp)
        )
        
        Spacer(modifier = Modifier.size(12.dp))
        
        Text(
            text = name,
            style = MaterialTheme.typography.bodyMedium,
            color = MaterialTheme.colorScheme.onSurface,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

@Composable
fun LocalMusicDirectoriesDialog(
    setShowDialog: (Boolean) -> Unit
) {
    val context = LocalContext.current
    val coroutineScope = rememberCoroutineScope()
    val settingsManager = SettingsManager(context)

    val scanDirectories by settingsManager.scanDirectoriesFlow.collectAsState(emptySet())
    val autoScanEnabled by settingsManager.autoScanEnabledFlow.collectAsState(true)

    var showDirectoryPicker by remember { mutableStateOf(false) }

    Dialog(
        onDismissRequest = { setShowDialog(false) },
        properties = DialogProperties(usePlatformDefaultWidth = false)
    ) {
        Surface(
            modifier = Modifier
                .widthIn(min = 320.dp, max = 500.dp)
                .shadow(8.dp, RoundedCornerShape(12.dp))
                .clip(RoundedCornerShape(12.dp))
                .background(MaterialTheme.colorScheme.surface),
            color = Color.Transparent
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                // Header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Local Music Settings",
                        style = MaterialTheme.typography.headlineSmall,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    IconButton(onClick = { setShowDialog(false) }) {
                        Icon(
                            imageVector = Icons.Rounded.Close,
                            contentDescription = "Close",
                            tint = MaterialTheme.colorScheme.onSurface
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Auto scan toggle
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Auto Scan for Music",
                        style = MaterialTheme.typography.bodyLarge,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    Switch(
                        checked = autoScanEnabled,
                        onCheckedChange = {
                            coroutineScope.launch {
                                settingsManager.setAutoScanEnabled(it)
                            }
                        }
                    )
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Scan directories header
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Scan Directories",
                        style = MaterialTheme.typography.bodyLarge,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onSurface
                    )

                    Button(
                        onClick = { showDirectoryPicker = true },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.primary,
                            contentColor = MaterialTheme.colorScheme.onPrimary
                        )
                    ) {
                        Text("Add")
                    }
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Directory list
                LazyColumn(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(200.dp)
                ) {
                    items(scanDirectories.toList()) { directory ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 4.dp)
                                .background(
                                    MaterialTheme.colorScheme.surfaceVariant,
                                    RoundedCornerShape(8.dp)
                                )
                                .padding(12.dp),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = directory,
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                modifier = Modifier.weight(1f),
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )

                            IconButton(
                                onClick = {
                                    coroutineScope.launch {
                                        settingsManager.removeScanDirectory(directory)
                                    }
                                }
                            ) {
                                Icon(
                                    imageVector = Icons.Rounded.Close,
                                    contentDescription = "Remove directory",
                                    tint = MaterialTheme.colorScheme.error,
                                    modifier = Modifier.size(20.dp)
                                )
                            }
                        }
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Action buttons
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.End)
                ) {
                    Button(
                        onClick = {
                            coroutineScope.launch {
                                LocalProvider.getInstance().scanLocalFiles()
                            }
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = MaterialTheme.colorScheme.secondary,
                            contentColor = MaterialTheme.colorScheme.onSecondary
                        )
                    ) {
                        Text("Rescan")
                    }

                    Button(
                        onClick = { setShowDialog(false) }
                    ) {
                        Text("Done")
                    }
                }
            }
        }
    }

    if (showDirectoryPicker) {
        DirectoryPickerDialog(
            setShowDialog = { showDirectoryPicker = it },
            onDirectorySelected = { directory ->
                coroutineScope.launch {
                    settingsManager.addScanDirectory(directory)
                }
            }
        )
    }
}
