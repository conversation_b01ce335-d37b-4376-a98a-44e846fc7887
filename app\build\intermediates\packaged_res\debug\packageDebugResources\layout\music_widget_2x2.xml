<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/widget_background"
    android:padding="16dp">

    <!-- Album Art -->
    <ImageView
        android:id="@+id/widget_album_art_2x2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginBottom="12dp"
        android:scaleType="centerCrop"
        android:background="@drawable/widget_album_background"
        android:src="@drawable/ic_music_note"
        android:elevation="2dp" />

    <!-- Song Info -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="12dp">

        <TextView
            android:id="@+id/widget_song_title_2x2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="No song playing"
            android:textColor="@color/widget_text_primary"
            android:textSize="14sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center" />

        <TextView
            android:id="@+id/widget_artist_name_2x2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Unknown Artist"
            android:textColor="@color/widget_text_secondary"
            android:textSize="12sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:gravity="center"
            android:layout_marginTop="2dp" />

    </LinearLayout>

    <!-- Control Buttons -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <ImageButton
            android:id="@+id/widget_previous_2x2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_skip_previous"
            android:contentDescription="Previous"
            android:scaleType="centerInside"
            android:layout_marginEnd="12dp"
            android:elevation="1dp" />

        <ImageButton
            android:id="@+id/widget_play_pause_2x2"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_play_arrow"
            android:contentDescription="Play/Pause"
            android:scaleType="centerInside"
            android:layout_marginEnd="12dp"
            android:elevation="2dp" />

        <ImageButton
            android:id="@+id/widget_next_2x2"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:background="@drawable/widget_button_background"
            android:src="@drawable/ic_skip_next"
            android:contentDescription="Next"
            android:scaleType="centerInside"
            android:elevation="1dp" />

    </LinearLayout>

</LinearLayout>
